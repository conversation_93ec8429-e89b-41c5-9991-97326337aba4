# Troubleshooting Guide

This guide helps resolve common issues with the SmartHRMS CI/CD pipeline.

## Pipeline Issues

### 1. Pipeline Fails to Start

**Symptoms:**
- <PERSON> shows "Pipeline script not found"
- Build fails immediately without executing stages

**Solutions:**

```bash
# Check Jenkinsfile exists in repository root
ls -la Jenkinsfile

# Verify branch configuration in Jenkins
# Go to Jenkins → Pipeline → Configuration → Branch Sources

# Check shared library configuration
# Manage Jenkins → Configure System → Global Pipeline Libraries
```

**Common Causes:**
- <PERSON><PERSON><PERSON> not in repository root
- Incorrect branch discovery configuration
- Shared library not properly configured
- GitLab connectivity issues

### 2. Shared Library Not Found

**Symptoms:**
- Error: "Library jenkins-lib not found"
- <PERSON><PERSON><PERSON> fails at @Library directive

**Solutions:**

```bash
# Verify shared library configuration in Jenkins
# Manage Jenkins → Configure System → Global Pipeline Libraries

# Check library repository accessibility
git clone https://gitlab.company.com/devops/jenkins-lib.git

# Verify library structure
jenkins-lib/
├── vars/
│   └── deployApp.groovy
└── src/org/devops/
    └── DeployUtils.groovy
```

### 3. Credential Issues

**Symptoms:**
- "Credentials not found" errors
- Authentication failures to Harbor/GitLab

**Solutions:**

```bash
# Check credentials in Jenkins
# Manage Jenkins → Manage Credentials → Global

# Required credentials:
# - harbor-credentials (Username/Password)
# - ssh-deployer-key (SSH Private Key)
# - sonarqube-token (Secret Text)

# Test Harbor connectivity
docker login harbor.company.com
```

## Build Issues

### 1. Maven Build Failures

**Symptoms:**
- "mvnw: command not found"
- Compilation errors
- Dependency resolution failures

**Solutions:**

```bash
# Check Maven wrapper exists
ls -la mvnw mvnw.cmd

# Make Maven wrapper executable
chmod +x mvnw

# Test local build
./mvnw clean package -DskipTests

# Check Java version compatibility
java -version  # Should be Java 21
```

**Common Fixes:**

```bash
# Fix Maven wrapper permissions
git update-index --chmod=+x mvnw

# Update Maven wrapper
./mvnw wrapper:wrapper -Dmaven=3.9.4

# Clear Maven cache
rm -rf ~/.m2/repository
```

### 2. Docker Build Failures

**Symptoms:**
- "Docker daemon not available"
- "Base image not found"
- Build context too large

**Solutions:**

```bash
# Check Docker daemon status
sudo systemctl status docker

# Test Docker connectivity
docker info

# Check Dockerfile syntax
docker build --no-cache -t test .

# Reduce build context size
echo "target/" >> .dockerignore
echo ".git/" >> .dockerignore
```

### 3. SonarQube Integration Issues

**Symptoms:**
- "SonarQube server not found"
- Quality gate failures
- Authentication errors

**Solutions:**

```bash
# Test SonarQube connectivity
curl -I http://sonarqube.company.com:9000

# Check SonarQube configuration in Jenkins
# Manage Jenkins → Configure System → SonarQube servers

# Verify project exists in SonarQube
# Login to SonarQube → Projects → smarthrms-dev

# Test SonarQube scanner
sonar-scanner -Dsonar.projectKey=smarthrms-dev -Dsonar.sources=src
```

## Deployment Issues

### 1. SSH Connection Failures

**Symptoms:**
- "Permission denied (publickey)"
- "Connection refused"
- "Host key verification failed"

**Solutions:**

```bash
# Test SSH connectivity
ssh -i ~/.ssh/id_rsa deployer@**********

# Check SSH key permissions
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub

# Add host to known_hosts
ssh-keyscan -H ********** >> ~/.ssh/known_hosts

# Verify deployer user exists on target server
ssh root@********** "id deployer"
```

**SSH Key Setup:**

```bash
# Generate SSH key pair (if needed)
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa

# Copy public key to target servers
ssh-copy-id -i ~/.ssh/id_rsa.pub deployer@**********

# Test passwordless SSH
ssh deployer@********** "echo 'SSH connection successful'"
```

### 2. Docker Deployment Failures

**Symptoms:**
- "Container failed to start"
- "Port already in use"
- "Image not found"

**Solutions:**

```bash
# Check container status
docker ps -a | grep smarthrms

# Check container logs
docker logs smarthrms-app

# Check port availability
netstat -tlnp | grep 8089

# Stop conflicting containers
docker stop $(docker ps -q --filter "publish=8089")

# Pull image manually
docker pull harbor.company.com/myapps/smarthrms-dev:latest
```

### 3. Application Startup Issues

**Symptoms:**
- Health check failures
- Database connection errors
- Application not responding

**Solutions:**

```bash
# Check application logs
docker logs smarthrms-app

# Test database connectivity
docker exec smarthrms-app curl -f http://localhost:8089/health

# Check environment variables
docker exec smarthrms-app env | grep SPRING

# Restart application
docker restart smarthrms-app
```

## Health Check Issues

### 1. Health Endpoint Not Responding

**Symptoms:**
- HTTP 404 on /health endpoint
- Connection timeouts
- HTTP 503 Service Unavailable

**Diagnostic Steps:**

```bash
# Check if application is running
docker ps | grep smarthrms-app

# Check application logs
docker logs smarthrms-app --tail 50

# Test health endpoint directly
curl -v http://localhost:8089/health

# Check port binding
docker port smarthrms-app

# Test from inside container
docker exec smarthrms-app curl -f http://localhost:8089/health
```

**Common Solutions:**

```bash
# Restart application
docker restart smarthrms-app

# Check database connectivity
docker exec smarthrms-app ping mysql-prod

# Verify environment variables
docker exec smarthrms-app env | grep -E "SPRING|DB"

# Check application startup time
docker logs smarthrms-app | grep "Started DevApplication"
```

### 2. Database Connection Issues

**Symptoms:**
- "Connection refused" errors
- "Unknown database" errors
- "Access denied" errors

**Solutions:**

```bash
# Test database connectivity
mysql -h mysql-prod -u smarthrms_user -p smarthrms

# Check database server status
docker exec mysql-prod mysqladmin ping

# Verify database exists
docker exec mysql-prod mysql -u root -p -e "SHOW DATABASES;"

# Check user permissions
docker exec mysql-prod mysql -u root -p -e "SHOW GRANTS FOR 'smarthrms_user'@'%';"
```

## Environment-Specific Issues

### 1. Development Environment

**Common Issues:**
- Docker Compose services not starting
- Port conflicts
- Volume mount issues

**Solutions:**

```bash
# Check Docker Compose status
docker-compose ps

# View service logs
docker-compose logs smarthrms-app
docker-compose logs mysql

# Restart services
docker-compose down
docker-compose up -d

# Check port conflicts
netstat -tlnp | grep -E "3306|8089"
```

### 2. Production Environment

**Common Issues:**
- Resource constraints
- Network connectivity
- Security restrictions

**Solutions:**

```bash
# Check system resources
free -h
df -h
top

# Check network connectivity
ping ********
telnet ******** 8089

# Check firewall rules
sudo ufw status
sudo iptables -L
```

## Performance Issues

### 1. Slow Build Times

**Symptoms:**
- Builds taking longer than expected
- Maven dependency downloads
- Docker layer caching issues

**Solutions:**

```bash
# Enable Maven parallel builds
./mvnw -T 4 clean package

# Use Docker layer caching
docker build --cache-from harbor.company.com/myapps/smarthrms-dev:latest .

# Optimize Dockerfile
# Use multi-stage builds
# Minimize layer count
# Order layers by change frequency
```

### 2. Application Performance

**Symptoms:**
- Slow response times
- High memory usage
- Database connection pool exhaustion

**Solutions:**

```bash
# Check application metrics
curl http://localhost:8089/actuator/metrics

# Monitor container resources
docker stats smarthrms-app

# Check database connections
docker exec mysql-prod mysql -u root -p -e "SHOW PROCESSLIST;"

# Tune JVM settings
# Add to docker run: -e JAVA_OPTS="-Xmx2g -XX:+UseG1GC"
```

## Monitoring and Debugging

### 1. Log Analysis

**Application Logs:**
```bash
# Real-time application logs
docker logs -f smarthrms-app

# Search for errors
docker logs smarthrms-app 2>&1 | grep -i error

# Filter by timestamp
docker logs smarthrms-app --since="2023-01-01T00:00:00"
```

**Jenkins Logs:**
```bash
# Jenkins system logs
sudo tail -f /var/log/jenkins/jenkins.log

# Build-specific logs
# Available in Jenkins UI under specific build
```

**System Logs:**
```bash
# System logs
sudo journalctl -u jenkins -f
sudo journalctl -u docker -f

# Application-specific logs
sudo journalctl -u smarthrms-app -f
```

### 2. Network Debugging

```bash
# Test connectivity between services
docker exec smarthrms-app ping mysql-prod
docker exec smarthrms-app nslookup harbor.company.com

# Check network configuration
docker network ls
docker network inspect smarthrms-network

# Test port accessibility
telnet ********** 8089
nc -zv ********** 8089
```

### 3. Resource Monitoring

```bash
# System resources
htop
iotop
nethogs

# Docker resources
docker system df
docker system events

# Application-specific monitoring
curl http://localhost:8089/actuator/health
curl http://localhost:8089/actuator/metrics
```

## Recovery Procedures

### 1. Application Recovery

```bash
# Quick application restart
docker restart smarthrms-app

# Full redeployment
./deployment/scripts/deploy.sh prod smarthrms-dev:latest ********

# Rollback to previous version
docker run -d --name smarthrms-app-rollback \
  -p 8089:8089 \
  harbor.company.com/myapps/smarthrms-dev:previous-tag
```

### 2. Database Recovery

```bash
# Database backup
docker exec mysql-prod mysqldump -u root -p smarthrms > backup.sql

# Database restore
docker exec -i mysql-prod mysql -u root -p smarthrms < backup.sql

# Check database integrity
docker exec mysql-prod mysqlcheck -u root -p --all-databases
```

### 3. Jenkins Recovery

```bash
# Restart Jenkins
sudo systemctl restart jenkins

# Restore from backup
sudo tar -xzf jenkins-backup.tar.gz -C /var/lib/jenkins/

# Rebuild pipeline
# Go to Jenkins → Pipeline → Scan Repository Now
```

## Getting Help

### 1. Log Collection

When reporting issues, collect these logs:

```bash
# Application logs
docker logs smarthrms-app > app.log

# Jenkins build logs
# Download from Jenkins UI

# System information
docker info > docker-info.txt
docker version > docker-version.txt
free -h > memory-info.txt
df -h > disk-info.txt
```

### 2. Health Check Report

```bash
# Run comprehensive health check
./deployment/scripts/health-check.sh localhost > health-report.txt

# Check all environments
for server in ********** ********** ********* ******** ******** ********; do
  echo "=== $server ===" >> health-report.txt
  ./deployment/scripts/health-check.sh $server >> health-report.txt
done
```

### 3. Contact Information

- **DevOps Team**: <EMAIL>
- **Development Team**: <EMAIL>
- **Emergency Contact**: +1-555-0123

### 4. Escalation Process

1. **Level 1**: Check this troubleshooting guide
2. **Level 2**: Contact DevOps team with logs
3. **Level 3**: Emergency escalation for production issues
4. **Level 4**: Vendor support (if applicable)
