# Complete Setup Guide

This guide provides step-by-step instructions for setting up the complete CI/CD pipeline for the SmartHRMS application.

## Prerequisites

### Required Software

- **Jenkins** 2.400+ with Pipeline and Multibranch Pipeline plugins
- **Docker** 20.10+ on all target servers
- **Harbor Registry** or compatible Docker registry
- **SonarQube** 9.0+ for code quality analysis
- **GitLab** for source code management
- **SSH access** to all target servers

### Required Credentials

- Harbor registry username/password
- SSH private key for server access
- SonarQube authentication token
- GitLab access token (if using private repositories)

## Step 1: Infrastructure Setup

### 1.1 Harbor Registry Setup

```bash
# Install Harbor (example for Ubuntu)
wget https://github.com/goharbor/harbor/releases/download/v2.8.0/harbor-offline-installer-v2.8.0.tgz
tar xvf harbor-offline-installer-v2.8.0.tgz
cd harbor

# Configure Harbor
cp harbor.yml.tmpl harbor.yml
# Edit harbor.yml with your domain and settings

# Install Harbor
sudo ./install.sh
```

Create required projects in Harbor:
- `myapps` - for application images
- `build-images` - for build container images

### 1.2 SonarQube Setup

```bash
# Using Docker
docker run -d --name sonarqube \
  -p 9000:9000 \
  -e SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true \
  sonarqube:9.9-community

# Access SonarQube at http://localhost:9000
# Default credentials: admin/admin
```

Create project in SonarQube:
- Project key: `smarthrms-dev`
- Generate authentication token for Jenkins

### 1.3 Target Servers Setup

Run the setup script on each target server:

```bash
# Development servers
./deployment/scripts/setup-server.sh **********
./deployment/scripts/setup-server.sh **********

# Test server
./deployment/scripts/setup-server.sh *********

# Pre-production server
./deployment/scripts/setup-server.sh ********

# Production servers
./deployment/scripts/setup-server.sh ********
./deployment/scripts/setup-server.sh ********
```

## Step 2: Jenkins Configuration

### 2.1 Install Required Plugins

Install these Jenkins plugins:
- Pipeline
- Multibranch Pipeline
- Git
- Docker Pipeline
- SonarQube Scanner
- Email Extension
- SSH Agent
- Credentials Binding

### 2.2 Configure Global Tools

**Maven Configuration**:
- Go to Manage Jenkins → Global Tool Configuration
- Add Maven installation (version 3.9.4)
- Name: `Maven-3.9.4`

**JDK Configuration**:
- Add JDK installation (Java 21)
- Name: `Java-21`

**SonarQube Scanner**:
- Add SonarQube Scanner installation
- Name: `SonarQube-Scanner`

### 2.3 Configure SonarQube Integration

**Add SonarQube Server**:
- Go to Manage Jenkins → Configure System
- Add SonarQube server:
  - Name: `SonarQube`
  - Server URL: `http://sonarqube.company.com:9000`
  - Authentication token: Use credentials

### 2.4 Configure Global Pipeline Library

**Add Shared Library**:
- Go to Manage Jenkins → Configure System
- Global Pipeline Libraries section:
  - Name: `jenkins-lib`
  - Default version: `main`
  - Retrieval method: Modern SCM
  - Source Code Management: Git
  - Repository URL: `<your-gitlab-repo-url>`

### 2.5 Configure Credentials

Add the following credentials in Jenkins:

**Harbor Registry Credentials**:
- Type: Username with password
- ID: `harbor-credentials`
- Username: `<harbor-username>`
- Password: `<harbor-password>`

**SSH Private Key**:
- Type: SSH Username with private key
- ID: `ssh-deployer-key`
- Username: `deployer`
- Private Key: Upload your SSH private key

**SonarQube Token**:
- Type: Secret text
- ID: `sonarqube-token`
- Secret: `<sonarqube-auth-token>`

## Step 3: GitLab Repository Setup

### 3.1 Repository Structure

Ensure your GitLab repository has this structure:

```
your-repo/
├── jenkins-lib/           # Shared library (can be separate repo)
├── src/                   # Application source
├── Jenkinsfile           # Pipeline definition
├── docker-compose.yml   # Local development
├── Dockerfile           # Application container
└── deployment/          # Deployment configurations
```

### 3.2 Branch Protection

Configure branch protection rules:
- `main`: Require merge requests, no direct pushes
- `develop`: Allow direct pushes for development
- `release/*`: Require merge requests
- `tag/*`: Protected tags

## Step 4: Jenkins Pipeline Setup

### 4.1 Create Multibranch Pipeline

1. **New Item** → **Multibranch Pipeline**
2. **Name**: `smarthrms-dev-pipeline`
3. **Branch Sources** → **Git**:
   - Project Repository: `<your-gitlab-repo-url>`
   - Credentials: Add GitLab credentials if needed
4. **Build Configuration**:
   - Mode: by Jenkinsfile
   - Script Path: `Jenkinsfile`
5. **Scan Multibranch Pipeline Triggers**:
   - Periodically if not otherwise run: 1 minute
6. **Save**

### 4.2 Configure Webhooks (Optional)

In GitLab project settings:
- Go to Settings → Webhooks
- URL: `http://jenkins.company.com/project/smarthrms-dev-pipeline`
- Trigger: Push events, Tag push events, Merge request events

## Step 5: Build Container Images

### 5.1 Create Build Container

Create and push build container to Harbor:

```dockerfile
# Dockerfile.build
FROM maven:3.9.4-openjdk-21-slim

# Install additional tools
RUN apt-get update && apt-get install -y \
    curl \
    git \
    docker.io \
    && rm -rf /var/lib/apt/lists/*

# Install SonarQube Scanner
RUN curl -o sonar-scanner.zip -L https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-4.8.0.2856-linux.zip \
    && unzip sonar-scanner.zip \
    && mv sonar-scanner-4.8.0.2856-linux /opt/sonar-scanner \
    && ln -s /opt/sonar-scanner/bin/sonar-scanner /usr/local/bin/sonar-scanner

WORKDIR /workspace
```

Build and push:

```bash
docker build -f Dockerfile.build -t harbor.company.com/build-images/java21:latest .
docker push harbor.company.com/build-images/java21:latest
```

## Step 6: Testing the Pipeline

### 6.1 Test Local Development

```bash
# Start local environment
docker-compose up -d

# Test application
curl http://localhost:8089/health
curl http://localhost:8089/api/ping
```

### 6.2 Test Pipeline Execution

1. **Push to develop branch**:
   ```bash
   git checkout develop
   git push origin develop
   ```
   - Should trigger build and deploy to dev servers

2. **Create release branch**:
   ```bash
   git checkout -b release/1.0.0
   git push origin release/1.0.0
   ```
   - Should trigger build and deploy to test server

3. **Create tag**:
   ```bash
   git tag tag/1.0.0
   git push origin tag/1.0.0
   ```
   - Should deploy existing image to pre-prod and prod servers

### 6.3 Verify Deployments

```bash
# Check each server
./deployment/scripts/health-check.sh **********
./deployment/scripts/health-check.sh **********
./deployment/scripts/health-check.sh *********
./deployment/scripts/health-check.sh ********
./deployment/scripts/health-check.sh ********
./deployment/scripts/health-check.sh ********
```

## Step 7: Monitoring and Maintenance

### 7.1 Log Monitoring

Set up log aggregation:
- Use ELK stack or similar for centralized logging
- Configure log rotation on all servers
- Monitor application and deployment logs

### 7.2 Backup Strategy

- Regular database backups
- Harbor registry backup
- Jenkins configuration backup
- Source code backup (GitLab)

### 7.3 Security Considerations

- Regular security updates on all servers
- Rotate SSH keys and passwords regularly
- Use secrets management for sensitive data
- Enable HTTPS for all services
- Configure firewall rules properly

## Troubleshooting

### Common Issues

1. **Pipeline fails to start**:
   - Check Jenkins logs
   - Verify shared library configuration
   - Check GitLab connectivity

2. **Docker build fails**:
   - Check Dockerfile syntax
   - Verify base image availability
   - Check network connectivity to registries

3. **Deployment fails**:
   - Check SSH connectivity
   - Verify server setup
   - Check Docker daemon status on target servers

4. **Health checks fail**:
   - Check application logs
   - Verify database connectivity
   - Check network connectivity between services

### Getting Help

- Check Jenkins build logs for detailed error messages
- Review application logs using `docker logs smarthrms-app`
- Use health check script for diagnostics
- Check server connectivity and Docker status

## Next Steps

After successful setup:

1. Configure monitoring and alerting
2. Set up automated backups
3. Implement security scanning
4. Add performance testing
5. Configure log aggregation
6. Set up disaster recovery procedures
