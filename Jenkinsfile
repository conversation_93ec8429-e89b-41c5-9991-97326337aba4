@Library('jenkins-lib') _

/**
 * Jenkins Pipeline for smarthrms-dev application
 * 
 * Branch Strategy:
 * - develop: Full build + deploy to dev environment
 * - release/*: Full build + deploy to test environment  
 * - tag/*: Deploy only (no build) to preprod + prod environments
 * 
 * This pipeline uses the shared library 'jenkins-lib' which should be
 * configured in Jenkins Global Pipeline Libraries.
 */

pipeline {
    agent any
    
    options {
        // Keep builds for 30 days or 10 builds
        buildDiscarder(logRotator(daysToKeepStr: '30', numToKeepStr: '10'))
        
        // Timeout the build after 30 minutes
        timeout(time: 30, unit: 'MINUTES')
        
        // Skip default checkout
        skipDefaultCheckout(true)
        
        // Timestamps in console output
        timestamps()
    }
    
    environment {
        // Application configuration
        APP_NAME = 'smarthrms-dev'
        APP_PORT = '8089'
        
        // Harbor registry configuration
        HARBOR_REGISTRY = 'harbor.company.com'
        HARBOR_PROJECT = 'myapps'
        ARTIFACT_REPO_URL = "${HARBOR_REGISTRY}/${HARBOR_PROJECT}"
        
        // Build configuration
        BUILD_CONTAINER_IMAGE = "${HARBOR_REGISTRY}/build-images/java21:latest"
        
        // SonarQube configuration
        SONAR_PROJECT_KEY = 'smarthrms-dev'
        
        // Docker image tag
        DOCKER_IMAGE_TAG = "${APP_NAME}:${BUILD_NUMBER}"
    }
    
    stages {
        stage('Pipeline Info') {
            steps {
                script {
                    echo "=== Pipeline Information ==="
                    echo "Application: ${APP_NAME}"
                    echo "Branch: ${env.BRANCH_NAME}"
                    echo "Build Number: ${env.BUILD_NUMBER}"
                    echo "Build URL: ${env.BUILD_URL}"
                    echo "Workspace: ${env.WORKSPACE}"
                    echo "Harbor Registry: ${HARBOR_REGISTRY}"
                    echo "Image Tag: ${DOCKER_IMAGE_TAG}"
                    echo "=========================="
                }
            }
        }
        
        stage('Deploy Application') {
            steps {
                script {
                    // Call the shared library function with configuration
                    deployApp([
                        // Application configuration
                        appName: env.APP_NAME,
                        appType: 'springboot',
                        appPort: env.APP_PORT.toInteger(),
                        
                        // Build configuration
                        buildContainerImage: env.BUILD_CONTAINER_IMAGE,
                        
                        // Registry configuration
                        artifactRepoUrl: env.ARTIFACT_REPO_URL,
                        dockerImageTag: env.DOCKER_IMAGE_TAG,
                        
                        // SonarQube configuration
                        sonarProjectKey: env.SONAR_PROJECT_KEY,
                        
                        // Environment servers (as per devops.txt)
                        devServers: [
                            '**********',
                            '**********'
                        ],
                        testServers: [
                            '*********'
                        ],
                        preProdServers: [
                            '********'
                        ],
                        prodServers: [
                            '********',
                            '********'
                        ],
                        
                        // Notification configuration
                        emailRecipients: [
                            '<EMAIL>',
                            '<EMAIL>'
                        ]
                    ])
                }
            }
        }
    }
    
    post {
        always {
            script {
                echo "=== Pipeline completed ==="
                echo "Final Status: ${currentBuild.result ?: 'SUCCESS'}"
                echo "Duration: ${currentBuild.durationString}"
            }
        }
        
        success {
            script {
                echo "✅ Pipeline completed successfully!"
            }
        }
        
        failure {
            script {
                echo "❌ Pipeline failed!"
                
                // Additional failure notification can be added here
                // The shared library already handles email notifications
            }
        }
        
        cleanup {
            script {
                // Clean up workspace if needed
                echo "=== Cleaning up workspace ==="
                
                // Remove any temporary Docker images
                sh '''
                    # Clean up dangling images
                    docker image prune -f || true
                    
                    # Clean up old containers
                    docker container prune -f || true
                ''' 
            }
        }
    }
}
