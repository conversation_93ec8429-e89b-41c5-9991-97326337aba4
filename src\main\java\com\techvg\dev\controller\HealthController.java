package com.techvg.dev.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
public class HealthController {

    @Autowired
    private DataSource dataSource;

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // Check database connectivity
            try (Connection connection = dataSource.getConnection()) {
                boolean isValid = connection.isValid(5); // 5 seconds timeout
                
                health.put("status", isValid ? "UP" : "DOWN");
                health.put("database", isValid ? "CONNECTED" : "DISCONNECTED");
                health.put("timestamp", LocalDateTime.now().toString());
                health.put("application", "smarthrms-dev");
                health.put("version", "1.0.0");
                
                if (isValid) {
                    return ResponseEntity.ok(health);
                } else {
                    return ResponseEntity.status(503).body(health);
                }
            }
        } catch (SQLException e) {
            health.put("status", "DOWN");
            health.put("database", "ERROR");
            health.put("error", e.getMessage());
            health.put("timestamp", LocalDateTime.now().toString());
            health.put("application", "smarthrms-dev");
            health.put("version", "1.0.0");
            
            return ResponseEntity.status(503).body(health);
        }
    }

    @GetMapping("/health/simple")
    public ResponseEntity<String> simpleHealth() {
        try {
            try (Connection connection = dataSource.getConnection()) {
                boolean isValid = connection.isValid(5);
                return isValid ? ResponseEntity.ok("OK") : ResponseEntity.status(503).body("DOWN");
            }
        } catch (SQLException e) {
            return ResponseEntity.status(503).body("DOWN");
        }
    }
}
