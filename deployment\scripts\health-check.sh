#!/bin/bash

# Health check script for smarthrms-dev application
# Usage: ./health-check.sh <server_ip> [port]

set -e

# Configuration
APP_PORT=${2:-8089}
SERVER_IP=${1:-"localhost"}
MAX_RETRIES=30
RETRY_INTERVAL=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate arguments
if [ $# -lt 1 ]; then
    log_error "Usage: $0 <server_ip> [port]"
    log_error "Example: $0 ********** 8089"
    exit 1
fi

log_info "Starting health check for ${SERVER_IP}:${APP_PORT}"

# Health check function
check_health() {
    local url="http://${SERVER_IP}:${APP_PORT}/health"
    local response
    local http_code
    
    log_info "Checking health endpoint: $url"
    
    for i in $(seq 1 $MAX_RETRIES); do
        log_info "Attempt $i/$MAX_RETRIES: Checking health..."
        
        # Make HTTP request and capture response and status code
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" --connect-timeout 5 --max-time 10 "$url" 2>/dev/null || echo "HTTPSTATUS:000")
        
        # Extract HTTP status code
        http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
        
        # Extract response body
        body=$(echo "$response" | sed 's/HTTPSTATUS:[0-9]*$//')
        
        if [ "$http_code" = "200" ]; then
            log_success "Health check passed!"
            log_info "Response: $body"
            return 0
        else
            log_warning "Health check failed with HTTP code: $http_code"
            if [ -n "$body" ]; then
                log_warning "Response: $body"
            fi
        fi
        
        if [ $i -lt $MAX_RETRIES ]; then
            log_info "Retrying in $RETRY_INTERVAL seconds..."
            sleep $RETRY_INTERVAL
        fi
    done
    
    log_error "Health check failed after $MAX_RETRIES attempts"
    return 1
}

# Additional API tests
test_api_endpoints() {
    local base_url="http://${SERVER_IP}:${APP_PORT}"
    
    log_info "Testing additional API endpoints..."
    
    # Test ping endpoint
    local ping_url="${base_url}/api/ping"
    log_info "Testing ping endpoint: $ping_url"
    
    local ping_response=$(curl -s -w "HTTPSTATUS:%{http_code}" --connect-timeout 5 --max-time 10 "$ping_url" 2>/dev/null || echo "HTTPSTATUS:000")
    local ping_code=$(echo "$ping_response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    local ping_body=$(echo "$ping_response" | sed 's/HTTPSTATUS:[0-9]*$//')
    
    if [ "$ping_code" = "200" ]; then
        log_success "Ping endpoint test passed!"
        log_info "Ping response: $ping_body"
    else
        log_warning "Ping endpoint test failed with HTTP code: $ping_code"
    fi
    
    # Test simple health endpoint
    local simple_health_url="${base_url}/health/simple"
    log_info "Testing simple health endpoint: $simple_health_url"
    
    local simple_response=$(curl -s -w "HTTPSTATUS:%{http_code}" --connect-timeout 5 --max-time 10 "$simple_health_url" 2>/dev/null || echo "HTTPSTATUS:000")
    local simple_code=$(echo "$simple_response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    local simple_body=$(echo "$simple_response" | sed 's/HTTPSTATUS:[0-9]*$//')
    
    if [ "$simple_code" = "200" ]; then
        log_success "Simple health endpoint test passed!"
        log_info "Simple health response: $simple_body"
    else
        log_warning "Simple health endpoint test failed with HTTP code: $simple_code"
    fi
}

# Container status check (if running locally)
check_container_status() {
    if [ "$SERVER_IP" = "localhost" ] || [ "$SERVER_IP" = "127.0.0.1" ]; then
        log_info "Checking local container status..."
        
        if docker ps | grep -q "smarthrms-app"; then
            log_success "Container is running"
            
            # Show container logs (last 10 lines)
            log_info "Recent container logs:"
            docker logs --tail 10 smarthrms-app 2>/dev/null || log_warning "Could not retrieve container logs"
        else
            log_error "Container is not running"
            return 1
        fi
    fi
}

# Main execution
log_info "=== Starting comprehensive health check ==="

# Check container status first (if local)
check_container_status

# Run health check
if check_health; then
    # Run additional API tests
    test_api_endpoints
    
    log_success "=== All health checks completed successfully! ==="
    log_info "Application is healthy and ready to serve requests"
    exit 0
else
    log_error "=== Health check failed! ==="
    exit 1
fi
