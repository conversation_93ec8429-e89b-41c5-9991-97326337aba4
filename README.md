# SmartHRMS DevOps Pipeline

A complete CI/CD pipeline implementation for the SmartHRMS Spring Boot application using <PERSON>, Docker, Harbor, and SonarQube.

## 🏗️ Architecture Overview

This project implements a comprehensive DevOps pipeline with the following components:

- **Application**: Spring Boot 3.5.6 with Java 21
- **CI/CD**: <PERSON> with Groovy shared library
- **Containerization**: Docker with multi-stage builds
- **Registry**: Harbor for container images and artifacts
- **Quality**: SonarQube for code analysis
- **Deployment**: Environment-specific deployment to multiple servers

## 🌿 Branching Strategy

| Branch Pattern | Build | Test | Scan | Deploy | Target Environment |
|---------------|-------|------|------|--------|-------------------|
| `develop` | ✅ | ✅ | ✅ | ✅ | Development (**********, **********) |
| `release/*` | ✅ | ✅ | ✅ | ✅ | Test/UAT (*********) |
| `tag/*` | ❌ | ❌ | ❌ | ✅ | Pre-Prod + Production (********, ********, ********) |

## 📁 Project Structure

```
jenkins_pipeline/
├── src/                           # Spring Boot application source
├── docker-compose.yml             # Local development environment
├── docker-compose.prod.yml        # Production environment
├── Dockerfile                     # Application container definition
├── Jenkinsfile                    # Main pipeline configuration
├── Jenkinsfile.simple             # Simplified pipeline configuration
├── jenkins-lib/                   # Jenkins shared library
│   ├── vars/
│   │   └── deployApp.groovy       # Main pipeline orchestrator
│   ├── src/org/devops/
│   │   └── DeployUtils.groovy     # Deployment utilities
│   └── resources/
│       └── default-config.yml     # Default configuration
├── deployment/                    # Deployment configurations
│   ├── environments/              # Environment-specific configs
│   │   ├── dev.env
│   │   ├── test.env
│   │   └── prod.env
│   └── scripts/                   # Deployment scripts
│       ├── deploy.sh
│       ├── health-check.sh
│       └── setup-server.sh
└── docs/                          # Documentation
    ├── SETUP.md                   # Setup instructions
    ├── JENKINS_SETUP.md           # Jenkins configuration
    └── TROUBLESHOOTING.md         # Common issues and solutions
```

## 🚀 Quick Start

### 1. Local Development

```bash
# Clone the repository
git clone <repository-url>
cd jenkins_pipeline

# Start local environment
docker-compose up -d

# Check application health
curl http://localhost:8089/health
```

### 2. Jenkins Pipeline Setup

1. **Configure Jenkins Shared Library**:
   - Go to Jenkins → Manage Jenkins → Configure System
   - Add Global Pipeline Library named `jenkins-lib`
   - Point to your GitLab repository containing the `jenkins-lib` folder

2. **Create Multibranch Pipeline**:
   - New Item → Multibranch Pipeline
   - Configure GitLab source
   - Jenkins will automatically detect `Jenkinsfile`

3. **Configure Credentials**:
   - `harbor-credentials`: Harbor registry username/password
   - `ssh-deployer-key`: SSH private key for server access
   - `sonarqube-token`: SonarQube authentication token

### 3. Server Preparation

```bash
# Setup target servers
./deployment/scripts/setup-server.sh **********  # Dev server 1
./deployment/scripts/setup-server.sh **********  # Dev server 2
./deployment/scripts/setup-server.sh *********   # Test server
./deployment/scripts/setup-server.sh ********    # Pre-prod server
./deployment/scripts/setup-server.sh ********    # Prod server 1
./deployment/scripts/setup-server.sh ********    # Prod server 2
```

## 🔧 Configuration

### Environment Variables

The pipeline uses environment-specific configuration files:

- `deployment/environments/dev.env` - Development settings
- `deployment/environments/test.env` - Test environment settings  
- `deployment/environments/prod.env` - Production settings

### Jenkins Configuration

Key Jenkins configurations required:

```groovy
// Global Pipeline Library
Name: jenkins-lib
Default version: main
Retrieval method: Modern SCM
Source Code Management: Git
Repository URL: <your-gitlab-repo-url>
```

### Harbor Registry

Configure Harbor registry access:

```bash
# Harbor configuration
HARBOR_REGISTRY=harbor.company.com
HARBOR_PROJECT=myapps
HARBOR_USERNAME=<username>
HARBOR_PASSWORD=<password>
```

## 🏃‍♂️ Running Deployments

### Automatic Deployment (via Jenkins)

Deployments are triggered automatically based on Git events:

- **Push to `develop`**: Deploys to development environment
- **Push to `release/1.0.0`**: Deploys to test environment
- **Create tag `tag/1.0.0`**: Deploys to pre-prod and production

### Manual Deployment

```bash
# Deploy to specific environment
./deployment/scripts/deploy.sh dev smarthrms-dev:123 **********

# Run health check
./deployment/scripts/health-check.sh **********

# Check application logs
docker logs smarthrms-app
```

## 🔍 Monitoring and Health Checks

### Health Endpoints

- **Detailed Health**: `http://<server>:8089/health`
- **Simple Health**: `http://<server>:8089/health/simple`
- **API Test**: `http://<server>:8089/api/ping`

### Automated Health Checks

The pipeline includes automated smoke tests that verify:

- Application startup and readiness
- Database connectivity
- API endpoint availability
- Container health status

## 📧 Notifications

Email notifications are sent for:

- ✅ Successful deployments
- ❌ Failed deployments
- ⚠️ Quality gate failures

Configure recipients in the Jenkinsfile:

```groovy
emailRecipients: [
    '<EMAIL>',
    '<EMAIL>'
]
```

## 🛠️ Troubleshooting

### Common Issues

1. **Container fails to start**:
   ```bash
   docker logs smarthrms-app
   ./deployment/scripts/health-check.sh localhost
   ```

2. **Database connection issues**:
   - Check environment variables in `.env` files
   - Verify database server accessibility
   - Check network connectivity

3. **Harbor registry access**:
   ```bash
   docker login harbor.company.com
   docker pull harbor.company.com/myapps/smarthrms-dev:latest
   ```

### Logs and Debugging

```bash
# Application logs
docker logs smarthrms-app

# Jenkins pipeline logs
# Available in Jenkins UI under specific build

# Server deployment logs
# Check SSH connectivity and deployment script output
```

## 📚 Additional Documentation

- [Detailed Setup Instructions](docs/SETUP.md)
- [Jenkins Configuration Guide](docs/JENKINS_SETUP.md)
- [Troubleshooting Guide](docs/TROUBLESHOOTING.md)

## 🤝 Contributing

1. Create feature branch from `develop`
2. Make changes and test locally
3. Create merge request to `develop`
4. Pipeline will automatically test and deploy to dev environment

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
