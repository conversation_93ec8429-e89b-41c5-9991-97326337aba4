version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: smarthrms-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: smarthrms
      MYSQL_USER: smarthrms_user
      MYSQL_PASSWORD: smarthrms_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - smarthrms-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Spring Boot Application
  smarthrms-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: smarthrms-dev
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *********************************
      SPRING_DATASOURCE_USERNAME: smarthrms_user
      SPRING_DATASOURCE_PASSWORD: smarthrms_pass
      SPRING_DATASOURCE_DRIVER_CLASS_NAME: com.mysql.cj.jdbc.Driver
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      SPRING_JPA_SHOW_SQL: true
      SERVER_PORT: 8089
    ports:
      - "8089:8089"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - smarthrms-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8089/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  mysql_data:

networks:
  smarthrms-network:
    driver: bridge
