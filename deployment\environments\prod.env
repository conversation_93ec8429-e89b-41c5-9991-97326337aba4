# Production Environment Configuration
ENVIRONMENT=prod
APP_NAME=smarthrms-dev
APP_PORT=8089

# Database Configuration
DB_HOST=mysql-prod
DB_PORT=3306
DB_NAME=smarthrms
DB_USERNAME=smarthrms_user
DB_PASSWORD=smarthrms_prod_pass

# Spring Boot Configuration
SPRING_PROFILES_ACTIVE=prod
SPRING_DATASOURCE_URL=jdbc:mysql://${DB_HOST}:${DB_PORT}/${DB_NAME}
SPRING_DATASOURCE_USERNAME=${DB_USERNAME}
SPRING_DATASOURCE_PASSWORD=${DB_PASSWORD}
SPRING_JPA_HIBERNATE_DDL_AUTO=validate
SPRING_JPA_SHOW_SQL=false

# Java Configuration
JAVA_OPTS=-Xms1024m -Xmx2048m -XX:+UseG1GC -XX:+UseStringDeduplication

# Logging Configuration
LOGGING_LEVEL_COM_TECHVG_DEV=WARN
LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WEB=ERROR

# Server Configuration
SERVER_PORT=${APP_PORT}

# Production specific settings
DEBUG_MODE=false
ENABLE_SWAGGER=false
SECURITY_ENABLED=true

# Monitoring and Health
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when_authorized
