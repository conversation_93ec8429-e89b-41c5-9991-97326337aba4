@Library('jenkins-lib') _

/**
 * Simplified <PERSON><PERSON><PERSON> for smarthrms-dev application
 * Uses minimal configuration with sensible defaults
 */

// Simple configuration - just call the shared library
deployApp([
    appName: 'smarthrms-dev',
    appType: 'springboot',
    buildContainerImage: 'harbor.company.com/build-images/java21:latest',
    sonarProjectKey: 'smarthrms-dev',
    artifactRepoUrl: 'harbor.company.com/myapps',
    dockerImageTag: 'smarthrms-dev:' + env.BUILD_NUMBER,
    devServers: ['**********', '**********'],
    testServers: ['*********'],
    preProdServers: ['********'],
    prodServers: ['********', '********'],
    appPort: 8089,
    emailRecipients: ['<EMAIL>', '<EMAIL>']
])
