/**
 * Main pipeline orchestrator for smarthrms-dev application
 * Supports branch-based deployment strategy:
 * - develop: deploy to dev environment
 * - release/*: deploy to test environment  
 * - tag/*: deploy to preprod + prod environments (no build)
 */
def call(Map config = [:]) {
    def branch = env.BRANCH_NAME ?: env.GIT_BRANCH?.replaceAll('origin/', '')
    def skipBuild = false
    def deployEnv
    def servers
    def imageTag = config.dockerImageTag ?: "${config.appName}:${env.BUILD_NUMBER}"
    
    echo "=== Starting deployment for branch: ${branch} ==="
    echo "=== Application: ${config.appName} ==="
    echo "=== Image Tag: ${imageTag} ==="
    
    // Determine environment and behavior based on branch
    if (branch?.startsWith("tag/")) {
        skipBuild = true
        deployEnv = "preprod"
        servers = config.preProdServers + config.prodServers
        // For tags, use the version from tag name
        def version = branch.replaceAll('tag/', '')
        imageTag = "${config.appName}:${version}"
        echo "=== Tag deployment detected - skipping build, deploying version: ${version} ==="
    } else if (branch?.startsWith("release/")) {
        deployEnv = "test"
        servers = config.testServers
        def version = branch.replaceAll('release/', '')
        imageTag = "${config.appName}:${version}"
        echo "=== Release deployment detected - full build and test deployment ==="
    } else if (branch == "develop") {
        deployEnv = "dev"
        servers = config.devServers
        echo "=== Development deployment detected - full build and dev deployment ==="
    } else {
        error "Unsupported branch: ${branch}. Supported patterns: develop, release/*, tag/*"
    }
    
    echo "=== Target Environment: ${deployEnv} ==="
    echo "=== Target Servers: ${servers.join(', ')} ==="
    
    node {
        try {
            if (!skipBuild) {
                stage("Pull Build Container") {
                    echo "Using build container: ${config.buildContainerImage}"
                    docker.image(config.buildContainerImage).inside('-v /var/run/docker.sock:/var/run/docker.sock') {
                        deployWorkflow(config, servers, skipBuild, imageTag, deployEnv)
                    }
                }
            } else {
                deployWorkflow(config, servers, skipBuild, imageTag, deployEnv)
            }
        } catch (Exception e) {
            currentBuild.result = 'FAILURE'
            echo "=== Pipeline failed with error: ${e.getMessage()} ==="
            
            // Send failure notification
            def utils = new org.devops.DeployUtils(this)
            utils.sendEmail(
                config.emailRecipients,
                "❌ Deployment FAILED for ${env.BRANCH_NAME}",
                """
                Deployment for ${env.BRANCH_NAME} FAILED!
                
                Application: ${config.appName}
                Environment: ${deployEnv}
                Image Tag: ${imageTag}
                Error: ${e.getMessage()}
                
                Build URL: ${env.BUILD_URL}
                """
            )
            throw e
        }
    }
}

def deployWorkflow(config, servers, skipBuild, imageTag, deployEnv) {
    def utils = new org.devops.DeployUtils(this)
    
    stage('Git Checkout') {
        echo "=== Checking out source code ==="
        checkout scm
        
        // Display current commit info
        sh 'git log -1 --oneline'
    }
    
    stage('Code Analysis') {
        echo "=== Analyzing code structure ==="
        utils.analyzeCode()
    }
    
    if (!skipBuild) {
        utils.buildProject(config.appType)
        utils.runUnitTests(config.appType)
        utils.runSonarScan(config.sonarProjectKey, config.appType)
        utils.uploadToArtifactory(config.artifactRepoUrl, imageTag)
    }
    
    utils.downloadContainerImage(config.artifactRepoUrl, imageTag)
    utils.deployToVMs(servers, config.artifactRepoUrl, imageTag, config.appPort)
    utils.smokeTest(servers, config.appPort)
    
    // Send success notification
    utils.sendEmail(
        config.emailRecipients,
        "✅ Deployment SUCCESS for ${env.BRANCH_NAME}",
        """
        Deployment for ${env.BRANCH_NAME} completed successfully!
        
        Application: ${config.appName}
        Environment: ${deployEnv}
        Image Tag: ${imageTag}
        Deployed to: ${servers.join(', ')}
        
        Health Check URLs:
        ${servers.collect { "http://${it}:${config.appPort}/health" }.join('\n')}
        
        Build URL: ${env.BUILD_URL}
        """
    )
    
    echo "=== Deployment completed successfully! ==="
}
