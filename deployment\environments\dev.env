# Development Environment Configuration
ENVIRONMENT=dev
APP_NAME=smarthrms-dev
APP_PORT=8089

# Database Configuration
DB_HOST=mysql-dev
DB_PORT=3306
DB_NAME=smarthrms
DB_USERNAME=smarthrms_user
DB_PASSWORD=smarthrms_pass

# Spring Boot Configuration
SPRING_PROFILES_ACTIVE=dev
SPRING_DATASOURCE_URL=jdbc:mysql://${DB_HOST}:${DB_PORT}/${DB_NAME}
SPRING_DATASOURCE_USERNAME=${DB_USERNAME}
SPRING_DATASOURCE_PASSWORD=${DB_PASSWORD}
SPRING_JPA_HIBERNATE_DDL_AUTO=update
SPRING_JPA_SHOW_SQL=true

# Java Configuration
JAVA_OPTS=-Xms256m -Xmx512m -XX:+UseG1GC

# Logging Configuration
LOGGING_LEVEL_COM_TECHVG_DEV=DEBUG
LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WEB=INFO

# Server Configuration
SERVER_PORT=${APP_PORT}

# Development specific settings
DEBUG_MODE=true
ENABLE_SWAGGER=true
