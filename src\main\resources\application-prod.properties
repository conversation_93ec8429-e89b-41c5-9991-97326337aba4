# Production environment configuration
spring.application.name=smarthrms-dev
server.port=8089

# Database configuration for Production
spring.datasource.url=${SPRING_DATASOURCE_URL:*************************************}
spring.datasource.username=${SPRING_DATASOURCE_USERNAME:smarthrms_user}
spring.datasource.password=${SPRING_DATASOURCE_PASSWORD:smarthrms_pass}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# Connection pool settings
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# Logging
logging.level.com.techvg.dev=INFO
logging.level.org.springframework.web=WARN
logging.level.org.hibernate=WARN

# Actuator endpoints (for monitoring)
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
