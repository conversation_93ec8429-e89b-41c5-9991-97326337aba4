package org.devops

/**
 * Deployment utilities for smarthrms-dev application
 * Handles build, test, scan, deploy, and notification operations
 */
class DeployUtils implements Serializable {
    def steps
    
    DeployUtils(steps) {
        this.steps = steps
    }
    
    /**
     * Analyze code structure and log important files
     */
    def analyzeCode() {
        steps.echo "=== Analyzing code structure ==="
        steps.sh '''
            echo "=== Project Structure ==="
            find . -maxdepth 2 -type f -name "*.xml" -o -name "Dockerfile" -o -name "Jenkinsfile" -o -name "*.yml" -o -name "*.yaml" | head -20
            
            echo "=== Maven Project Info ==="
            if [ -f "pom.xml" ]; then
                grep -E "<groupId>|<artifactId>|<version>" pom.xml | head -10
            fi
            
            echo "=== Docker Files ==="
            ls -la | grep -i docker || echo "No Docker files found"
        '''
    }
    
    /**
     * Build the project based on application type
     */
    def buildProject(String appType) {
        steps.stage("Build Project") {
            steps.echo "=== Building ${appType} application ==="
            
            if (appType == 'springboot') {
                steps.sh '''
                    echo "=== Maven Build Started ==="
                    ./mvnw clean package -DskipTests -B
                    
                    echo "=== Build Artifacts ==="
                    ls -la target/*.jar
                    
                    echo "=== Build completed successfully ==="
                '''
            } else if (appType == 'angular') {
                steps.sh '''
                    echo "=== NPM Build Started ==="
                    npm ci
                    npm run build --prod
                    
                    echo "=== Build Artifacts ==="
                    ls -la dist/
                    
                    echo "=== Build completed successfully ==="
                '''
            } else {
                steps.error("Unsupported application type: ${appType}")
            }
        }
    }
    
    /**
     * Run unit tests
     */
    def runUnitTests(String appType) {
        steps.stage("Unit Tests") {
            steps.echo "=== Running unit tests for ${appType} ==="
            
            if (appType == 'springboot') {
                steps.sh '''
                    echo "=== Maven Test Started ==="
                    ./mvnw test -B
                    
                    echo "=== Test Results ==="
                    if [ -d "target/surefire-reports" ]; then
                        find target/surefire-reports -name "*.xml" | wc -l
                        echo "Test report files found"
                    fi
                '''
                
                // Publish test results
                steps.publishTestResults testResultsPattern: 'target/surefire-reports/*.xml'
                
            } else if (appType == 'angular') {
                steps.sh '''
                    echo "=== NPM Test Started ==="
                    npm test -- --watch=false --browsers=ChromeHeadless
                    
                    echo "=== Test completed successfully ==="
                '''
            }
        }
    }
    
    /**
     * Run SonarQube quality scan
     */
    def runSonarScan(String sonarKey, String appType) {
        steps.stage("SonarQube Quality Gate") {
            steps.echo "=== Running SonarQube analysis for project: ${sonarKey} ==="
            
            steps.withSonarQubeEnv('SonarQube') {
                if (appType == 'springboot') {
                    steps.sh """
                        echo "=== SonarQube Maven Analysis ==="
                        ./mvnw sonar:sonar \\
                            -Dsonar.projectKey=${sonarKey} \\
                            -Dsonar.projectName=smarthrms-dev \\
                            -Dsonar.java.binaries=target/classes \\
                            -Dsonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml
                    """
                } else if (appType == 'angular') {
                    steps.sh """
                        echo "=== SonarQube Scanner Analysis ==="
                        sonar-scanner \\
                            -Dsonar.projectKey=${sonarKey} \\
                            -Dsonar.projectName=smarthrms-dev \\
                            -Dsonar.sources=src \\
                            -Dsonar.exclusions=**/*.spec.ts,**/*.test.ts,**/node_modules/**
                    """
                }
            }
            
            // Wait for quality gate
            steps.timeout(time: 5, unit: 'MINUTES') {
                def qg = steps.waitForQualityGate()
                if (qg.status != 'OK') {
                    steps.error "Pipeline aborted due to quality gate failure: ${qg.status}"
                }
            }
            
            steps.echo "=== SonarQube quality gate passed ==="
        }
    }

    /**
     * Upload artifacts to Harbor registry
     */
    def uploadToArtifactory(String repo, String tag) {
        steps.stage("Upload Artifact") {
            steps.echo "=== Building and pushing Docker image: ${repo}/${tag} ==="

            steps.withCredentials([steps.usernamePassword(
                credentialsId: 'harbor-credentials',
                usernameVariable: 'HARBOR_USER',
                passwordVariable: 'HARBOR_PASS'
            )]) {
                steps.sh """
                    echo "=== Docker Login ==="
                    echo \$HARBOR_PASS | docker login ${repo.split('/')[0]} -u \$HARBOR_USER --password-stdin

                    echo "=== Building Docker Image ==="
                    docker build -t ${repo}/${tag} .

                    echo "=== Pushing to Harbor Registry ==="
                    docker push ${repo}/${tag}

                    echo "=== Cleaning up local image ==="
                    docker rmi ${repo}/${tag} || true

                    echo "=== Upload completed successfully ==="
                """
            }
        }
    }

    /**
     * Download container image from Harbor registry
     */
    def downloadContainerImage(String repo, String tag) {
        steps.stage("Download Container Image") {
            steps.echo "=== Downloading container image: ${repo}/${tag} ==="

            steps.withCredentials([steps.usernamePassword(
                credentialsId: 'harbor-credentials',
                usernameVariable: 'HARBOR_USER',
                passwordVariable: 'HARBOR_PASS'
            )]) {
                steps.sh """
                    echo "=== Docker Login ==="
                    echo \$HARBOR_PASS | docker login ${repo.split('/')[0]} -u \$HARBOR_USER --password-stdin

                    echo "=== Pulling Docker Image ==="
                    docker pull ${repo}/${tag}

                    echo "=== Verifying Image ==="
                    docker images | grep ${tag.split(':')[0]} || echo "Image verification failed"

                    echo "=== Download completed successfully ==="
                """
            }
        }
    }

    /**
     * Deploy application to target VMs
     */
    def deployToVMs(List servers, String repo, String tag, int appPort) {
        steps.stage("Deploy to Servers") {
            steps.echo "=== Deploying to servers: ${servers.join(', ')} ==="

            steps.withCredentials([steps.sshUserPrivateKey(
                credentialsId: 'ssh-deployer-key',
                keyFileVariable: 'SSH_KEY',
                usernameVariable: 'SSH_USER'
            )]) {
                servers.each { vm ->
                    steps.echo "=== Deploying to server: ${vm} ==="

                    steps.sh """
                        echo "=== Connecting to ${vm} ==="
                        ssh -i \$SSH_KEY -o StrictHostKeyChecking=no \$SSH_USER@${vm} '
                            echo "=== Server: ${vm} - Starting deployment ==="

                            # Login to Harbor registry
                            echo "=== Logging into Harbor registry ==="
                            echo "${steps.env.HARBOR_PASS}" | docker login ${repo.split('/')[0]} -u "${steps.env.HARBOR_USER}" --password-stdin

                            # Pull latest image
                            echo "=== Pulling image: ${repo}/${tag} ==="
                            docker pull ${repo}/${tag}

                            # Stop and remove existing container
                            echo "=== Stopping existing container ==="
                            docker stop smarthrms-app || true
                            docker rm smarthrms-app || true

                            # Create network if not exists
                            echo "=== Creating Docker network ==="
                            docker network create smarthrms-network || true

                            # Run new container
                            echo "=== Starting new container ==="
                            docker run -d \\
                                --name smarthrms-app \\
                                --network smarthrms-network \\
                                -p ${appPort}:${appPort} \\
                                -e SPRING_PROFILES_ACTIVE=prod \\
                                -e SPRING_DATASOURCE_URL=************************************** \\
                                -e SPRING_DATASOURCE_USERNAME=smarthrms_user \\
                                -e SPRING_DATASOURCE_PASSWORD=smarthrms_pass \\
                                --restart unless-stopped \\
                                ${repo}/${tag}

                            # Wait for container to start
                            echo "=== Waiting for container to start ==="
                            sleep 10

                            # Check container status
                            echo "=== Container status ==="
                            docker ps | grep smarthrms-app || echo "Container not running"

                            echo "=== Server: ${vm} - Deployment completed ==="
                        '
                    """

                    steps.echo "=== Deployment to ${vm} completed ==="
                }
            }

            steps.echo "=== All deployments completed ==="
        }
    }

    /**
     * Run smoke tests on deployed applications
     */
    def smokeTest(List servers, int port) {
        steps.stage("Smoke Test") {
            steps.echo "=== Running smoke tests on ${servers.size()} servers ==="

            servers.each { vm ->
                steps.echo "=== Testing server: ${vm} ==="

                steps.retry(3) {
                    steps.sh """
                        echo "=== Health check for ${vm}:${port} ==="

                        # Wait for application to be ready
                        for i in {1..30}; do
                            echo "Attempt \$i: Checking health endpoint..."

                            if curl -f -s --connect-timeout 5 --max-time 10 http://${vm}:${port}/health; then
                                echo "✅ Health check passed for ${vm}"
                                break
                            else
                                echo "❌ Health check failed for ${vm}, retrying in 10 seconds..."
                                sleep 10
                            fi

                            if [ \$i -eq 30 ]; then
                                echo "❌ Health check failed after 30 attempts"
                                exit 1
                            fi
                        done

                        # Additional API tests
                        echo "=== Testing API endpoints ==="
                        curl -f -s http://${vm}:${port}/api/ping || echo "Warning: /api/ping endpoint failed"

                        echo "=== Smoke test completed for ${vm} ==="
                    """
                }

                steps.echo "=== Smoke test passed for ${vm} ==="
            }

            steps.echo "=== All smoke tests completed successfully ==="
        }
    }

    /**
     * Send email notification
     */
    def sendEmail(List recipients, String subject, String body) {
        steps.stage("Send Email Notification") {
            steps.echo "=== Sending email notification ==="
            steps.echo "Recipients: ${recipients.join(', ')}"
            steps.echo "Subject: ${subject}"

            try {
                steps.mail(
                    to: recipients.join(','),
                    subject: subject,
                    body: body,
                    mimeType: 'text/plain'
                )
                steps.echo "=== Email notification sent successfully ==="
            } catch (Exception e) {
                steps.echo "=== Warning: Failed to send email notification: ${e.getMessage()} ==="
                // Don't fail the pipeline if email fails
            }
        }
    }
}
