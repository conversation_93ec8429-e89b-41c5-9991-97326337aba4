# Test Environment Configuration
ENVIRONMENT=test
APP_NAME=smarthrms-dev
APP_PORT=8089

# Database Configuration
DB_HOST=mysql-test
DB_PORT=3306
DB_NAME=smarthrms
DB_USERNAME=smarthrms_user
DB_PASSWORD=smarthrms_test_pass

# Spring Boot Configuration
SPRING_PROFILES_ACTIVE=test
SPRING_DATASOURCE_URL=jdbc:mysql://${DB_HOST}:${DB_PORT}/${DB_NAME}
SPRING_DATASOURCE_USERNAME=${DB_USERNAME}
SPRING_DATASOURCE_PASSWORD=${DB_PASSWORD}
SPRING_JPA_HIBERNATE_DDL_AUTO=validate
SPRING_JPA_SHOW_SQL=false

# Java Configuration
JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC

# Logging Configuration
LOGGING_LEVEL_COM_TECHVG_DEV=INFO
LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WEB=WARN

# Server Configuration
SERVER_PORT=${APP_PORT}

# Test specific settings
DEBUG_MODE=false
ENABLE_SWAGGER=true
TEST_DATA_ENABLED=true
