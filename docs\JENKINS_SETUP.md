# Jenkins Configuration Guide

This guide provides detailed instructions for configuring <PERSON> for the SmartHRMS CI/CD pipeline.

## Jenkins Installation

### System Requirements

- **OS**: Ubuntu 20.04+ or CentOS 8+
- **Java**: OpenJDK 11 or 17
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 50GB+ for Jenkins home directory
- **Network**: Access to GitLab, Harbor, SonarQube, and target servers

### Installation Steps

#### Ubuntu/Debian

```bash
# Update system
sudo apt update

# Install Java
sudo apt install openjdk-11-jdk -y

# Add Jenkins repository
wget -q -O - https://pkg.jenkins.io/debian-stable/jenkins.io.key | sudo apt-key add -
sudo sh -c 'echo deb https://pkg.jenkins.io/debian-stable binary/ > /etc/apt/sources.list.d/jenkins.list'

# Install Jenkins
sudo apt update
sudo apt install jenkins -y

# Start Jenkins
sudo systemctl start jenkins
sudo systemctl enable jenkins

# Get initial admin password
sudo cat /var/lib/jenkins/secrets/initialAdminPassword
```

#### CentOS/RHEL

```bash
# Install Java
sudo yum install java-11-openjdk-devel -y

# Add Jenkins repository
sudo wget -O /etc/yum.repos.d/jenkins.repo https://pkg.jenkins.io/redhat-stable/jenkins.repo
sudo rpm --import https://pkg.jenkins.io/redhat-stable/jenkins.io.key

# Install Jenkins
sudo yum install jenkins -y

# Start Jenkins
sudo systemctl start jenkins
sudo systemctl enable jenkins
```

## Initial Jenkins Configuration

### 1. First-time Setup

1. Access Jenkins at `http://your-server:8080`
2. Enter the initial admin password
3. Install suggested plugins
4. Create admin user
5. Configure Jenkins URL

### 2. Install Required Plugins

Go to **Manage Jenkins** → **Manage Plugins** → **Available** and install:

#### Essential Plugins
- **Pipeline**: Core pipeline functionality
- **Multibranch Pipeline**: Support for multibranch pipelines
- **Git**: Git SCM integration
- **GitLab**: GitLab integration and webhooks
- **Docker Pipeline**: Docker integration in pipelines
- **SonarQube Scanner**: Code quality analysis
- **Email Extension**: Enhanced email notifications
- **SSH Agent**: SSH key management
- **Credentials Binding**: Secure credential handling

#### Recommended Plugins
- **Blue Ocean**: Modern UI for pipelines
- **Build Timeout**: Automatic build timeouts
- **Timestamper**: Timestamps in console output
- **Workspace Cleanup**: Automatic workspace cleanup
- **Pipeline Stage View**: Visual pipeline stages
- **Slack Notification**: Slack integration (optional)

### 3. Global Tool Configuration

Navigate to **Manage Jenkins** → **Global Tool Configuration**:

#### Maven Configuration
```
Name: Maven-3.9.4
Install automatically: ✓
Version: 3.9.4
```

#### JDK Configuration
```
Name: Java-21
Install automatically: ✓
Version: OpenJDK 21
```

#### Git Configuration
```
Name: Default
Path to Git executable: git
```

#### SonarQube Scanner
```
Name: SonarQube-Scanner
Install automatically: ✓
Version: Latest
```

#### Docker Configuration
```
Name: Docker
Install automatically: ✓
Docker version: latest
```

## Credentials Configuration

### 1. Harbor Registry Credentials

**Manage Jenkins** → **Manage Credentials** → **Global** → **Add Credentials**:

```
Kind: Username with password
Scope: Global
Username: harbor-username
Password: harbor-password
ID: harbor-credentials
Description: Harbor Registry Access
```

### 2. SSH Private Key for Deployment

```
Kind: SSH Username with private key
Scope: Global
Username: deployer
Private Key: [Upload your private key file]
ID: ssh-deployer-key
Description: SSH key for deployment servers
```

### 3. SonarQube Authentication Token

```
Kind: Secret text
Scope: Global
Secret: [Your SonarQube token]
ID: sonarqube-token
Description: SonarQube authentication token
```

### 4. GitLab Access Token (if needed)

```
Kind: GitLab API token
Scope: Global
API token: [Your GitLab token]
ID: gitlab-token
Description: GitLab API access
```

## System Configuration

### 1. Configure System Settings

**Manage Jenkins** → **Configure System**:

#### Jenkins Location
```
Jenkins URL: http://jenkins.company.com:8080
System Admin e-mail address: <EMAIL>
```

#### Global Properties
Add environment variables:
```
HARBOR_REGISTRY = harbor.company.com
SONAR_HOST_URL = http://sonarqube.company.com:9000
```

#### Email Configuration
```
SMTP server: smtp.company.com
Default user e-mail suffix: @company.com
Use SMTP Authentication: ✓
Username: <EMAIL>
Password: [SMTP password]
Use SSL: ✓
SMTP Port: 465
```

### 2. SonarQube Server Configuration

**Manage Jenkins** → **Configure System** → **SonarQube servers**:

```
Name: SonarQube
Server URL: http://sonarqube.company.com:9000
Server authentication token: [Select sonarqube-token credential]
```

### 3. Global Pipeline Libraries

**Manage Jenkins** → **Configure System** → **Global Pipeline Libraries**:

```
Name: jenkins-lib
Default version: main
Load implicitly: ✓
Allow default version to be overridden: ✓
Include @Library changes in job recent changes: ✓

Retrieval method: Modern SCM
Source Code Management: Git
Project Repository: https://gitlab.company.com/devops/jenkins-lib.git
Credentials: [Select GitLab credentials if private repo]
```

## Pipeline Configuration

### 1. Create Multibranch Pipeline

1. **New Item** → **Multibranch Pipeline**
2. **Name**: `smarthrms-dev-pipeline`

#### Branch Sources Configuration
```
Source: Git
Project Repository: https://gitlab.company.com/smarthrms/smarthrms-dev.git
Credentials: [Select GitLab credentials if needed]
```

#### Build Configuration
```
Mode: by Jenkinsfile
Script Path: Jenkinsfile
```

#### Scan Multibranch Pipeline Triggers
```
Periodically if not otherwise run: ✓
Interval: 1 minute
```

#### Property Strategy
```
All branches get the same properties: ✓
```

### 2. Configure Branch Discovery

#### Discover branches
```
Strategy: All branches
```

#### Discover pull requests from origin
```
Strategy: Merging the pull request with the current target branch revision
Trust: From users with Admin or Write permission
```

### 3. Pipeline Behavior Configuration

#### Orphaned Item Strategy
```
Discard old items: ✓
Days to keep old items: 30
Max # of old items to keep: 10
```

#### Health Metrics
```
Recursive: ✓
```

## Security Configuration

### 1. Configure Global Security

**Manage Jenkins** → **Configure Global Security**:

#### Authentication
```
Security Realm: Jenkins' own user database
Allow users to sign up: ✗ (for production)
```

#### Authorization
```
Authorization: Matrix-based security
```

#### Agent Protocols
```
Enable only: Java Web Start Agent Protocol/4 (TLS encryption)
```

### 2. User Management

Create users with appropriate permissions:

#### Admin Users
- Overall: Administer
- All permissions

#### Developer Users
- Overall: Read
- Job: Build, Cancel, Read, Workspace
- View: Read

#### Read-only Users
- Overall: Read
- Job: Read
- View: Read

### 3. Script Security

**Manage Jenkins** → **In-process Script Approval**:

- Review and approve any pending scripts
- Configure script security for shared libraries

## Monitoring and Maintenance

### 1. System Information

Monitor Jenkins health:
- **Manage Jenkins** → **System Information**
- Check memory usage, disk space, and system load
- Monitor build queue and executor status

### 2. Log Management

Configure logging:
- **Manage Jenkins** → **System Log**
- Set appropriate log levels for different components
- Configure log rotation

### 3. Backup Strategy

#### Jenkins Configuration Backup
```bash
# Backup Jenkins home directory
sudo tar -czf jenkins-backup-$(date +%Y%m%d).tar.gz /var/lib/jenkins/

# Backup specific configurations
sudo cp -r /var/lib/jenkins/jobs/ /backup/jenkins-jobs/
sudo cp -r /var/lib/jenkins/users/ /backup/jenkins-users/
sudo cp /var/lib/jenkins/config.xml /backup/jenkins-config.xml
```

#### Automated Backup Script
```bash
#!/bin/bash
# jenkins-backup.sh

BACKUP_DIR="/backup/jenkins"
JENKINS_HOME="/var/lib/jenkins"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Stop Jenkins (optional for consistency)
sudo systemctl stop jenkins

# Create backup
sudo tar -czf $BACKUP_DIR/jenkins-backup-$DATE.tar.gz $JENKINS_HOME

# Start Jenkins
sudo systemctl start jenkins

# Keep only last 7 backups
find $BACKUP_DIR -name "jenkins-backup-*.tar.gz" -mtime +7 -delete

echo "Backup completed: jenkins-backup-$DATE.tar.gz"
```

### 4. Performance Tuning

#### JVM Options
Edit `/etc/default/jenkins`:

```bash
# Increase heap size
JAVA_ARGS="-Xmx4g -Xms2g"

# Garbage collection tuning
JAVA_ARGS="$JAVA_ARGS -XX:+UseG1GC -XX:+UseStringDeduplication"

# Jenkins-specific options
JENKINS_ARGS="--sessionTimeout=1440 --sessionEviction=86400"
```

#### Executor Configuration
- Set appropriate number of executors based on server capacity
- Use labels for different types of builds
- Configure node usage strategy

## Troubleshooting

### Common Issues

#### 1. Plugin Installation Failures
```bash
# Check Jenkins logs
sudo tail -f /var/log/jenkins/jenkins.log

# Restart Jenkins
sudo systemctl restart jenkins
```

#### 2. Permission Issues
```bash
# Fix Jenkins permissions
sudo chown -R jenkins:jenkins /var/lib/jenkins/
sudo chmod -R 755 /var/lib/jenkins/
```

#### 3. Memory Issues
```bash
# Check memory usage
free -h
ps aux | grep jenkins

# Increase JVM heap size in /etc/default/jenkins
JAVA_ARGS="-Xmx8g -Xms4g"
```

#### 4. Network Connectivity
```bash
# Test connectivity from Jenkins server
curl -I https://gitlab.company.com
curl -I http://harbor.company.com
curl -I http://sonarqube.company.com:9000

# Check firewall rules
sudo ufw status
```

### Log Locations

- **Jenkins logs**: `/var/log/jenkins/jenkins.log`
- **Build logs**: Available in Jenkins UI
- **System logs**: `/var/log/syslog` or `journalctl -u jenkins`

### Performance Monitoring

Monitor these metrics:
- Build queue length
- Average build time
- Executor utilization
- Memory and CPU usage
- Disk space usage

## Best Practices

### 1. Pipeline Design
- Use declarative pipelines when possible
- Implement proper error handling
- Use parallel stages for independent tasks
- Implement proper cleanup in post sections

### 2. Security
- Regularly update Jenkins and plugins
- Use least privilege principle for user permissions
- Secure credentials properly
- Enable audit logging

### 3. Maintenance
- Regular backups
- Monitor system resources
- Keep plugins updated
- Clean up old builds and workspaces

### 4. Performance
- Use appropriate number of executors
- Implement build caching where possible
- Use pipeline libraries for code reuse
- Monitor and optimize build times
