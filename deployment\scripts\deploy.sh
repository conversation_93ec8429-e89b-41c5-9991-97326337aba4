#!/bin/bash

# Deployment script for smarthrms-dev application
# Usage: ./deploy.sh <environment> <image_tag> [server_ip]

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
APP_NAME="smarthrms-dev"
CONTAINER_NAME="smarthrms-app"
NETWORK_NAME="smarthrms-network"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate arguments
if [ $# -lt 2 ]; then
    log_error "Usage: $0 <environment> <image_tag> [server_ip]"
    log_error "Example: $0 dev smarthrms-dev:123 **********"
    exit 1
fi

ENVIRONMENT=$1
IMAGE_TAG=$2
SERVER_IP=${3:-"localhost"}

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|test|prod)$ ]]; then
    log_error "Invalid environment: $ENVIRONMENT. Must be dev, test, or prod"
    exit 1
fi

log_info "Starting deployment..."
log_info "Environment: $ENVIRONMENT"
log_info "Image Tag: $IMAGE_TAG"
log_info "Server IP: $SERVER_IP"

# Load environment variables
ENV_FILE="${SCRIPT_DIR}/../environments/${ENVIRONMENT}.env"
if [ -f "$ENV_FILE" ]; then
    log_info "Loading environment variables from $ENV_FILE"
    source "$ENV_FILE"
else
    log_warning "Environment file not found: $ENV_FILE"
fi

# Harbor registry configuration
HARBOR_REGISTRY="harbor.company.com"
HARBOR_PROJECT="myapps"
FULL_IMAGE_NAME="${HARBOR_REGISTRY}/${HARBOR_PROJECT}/${IMAGE_TAG}"

log_info "Full image name: $FULL_IMAGE_NAME"

# Function to deploy locally
deploy_local() {
    log_info "Deploying locally..."
    
    # Create network if it doesn't exist
    if ! docker network ls | grep -q "$NETWORK_NAME"; then
        log_info "Creating Docker network: $NETWORK_NAME"
        docker network create "$NETWORK_NAME"
    fi
    
    # Stop and remove existing container
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        log_info "Stopping existing container: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME" || true
        docker rm "$CONTAINER_NAME" || true
    fi
    
    # Pull latest image
    log_info "Pulling image: $FULL_IMAGE_NAME"
    docker pull "$FULL_IMAGE_NAME"
    
    # Run new container
    log_info "Starting new container: $CONTAINER_NAME"
    docker run -d \
        --name "$CONTAINER_NAME" \
        --network "$NETWORK_NAME" \
        -p "${APP_PORT}:${APP_PORT}" \
        --env-file "$ENV_FILE" \
        --restart unless-stopped \
        "$FULL_IMAGE_NAME"
    
    # Wait for container to start
    log_info "Waiting for container to start..."
    sleep 10
    
    # Check container status
    if docker ps | grep -q "$CONTAINER_NAME"; then
        log_success "Container started successfully"
    else
        log_error "Container failed to start"
        docker logs "$CONTAINER_NAME"
        exit 1
    fi
}

# Function to deploy to remote server
deploy_remote() {
    local server_ip=$1
    log_info "Deploying to remote server: $server_ip"
    
    # Copy environment file to remote server
    scp "$ENV_FILE" "deployer@${server_ip}:/tmp/${ENVIRONMENT}.env"
    
    # Execute deployment on remote server
    ssh "deployer@${server_ip}" << EOF
        set -e
        
        echo "=== Starting deployment on ${server_ip} ==="
        
        # Create network if it doesn't exist
        docker network create ${NETWORK_NAME} || true
        
        # Stop and remove existing container
        docker stop ${CONTAINER_NAME} || true
        docker rm ${CONTAINER_NAME} || true
        
        # Pull latest image
        echo "=== Pulling image: ${FULL_IMAGE_NAME} ==="
        docker pull ${FULL_IMAGE_NAME}
        
        # Run new container
        echo "=== Starting new container ==="
        docker run -d \\
            --name ${CONTAINER_NAME} \\
            --network ${NETWORK_NAME} \\
            -p ${APP_PORT}:${APP_PORT} \\
            --env-file /tmp/${ENVIRONMENT}.env \\
            --restart unless-stopped \\
            ${FULL_IMAGE_NAME}
        
        # Wait for container to start
        sleep 10
        
        # Check container status
        if docker ps | grep -q ${CONTAINER_NAME}; then
            echo "=== Container started successfully on ${server_ip} ==="
        else
            echo "=== Container failed to start on ${server_ip} ==="
            docker logs ${CONTAINER_NAME}
            exit 1
        fi
        
        # Clean up
        rm -f /tmp/${ENVIRONMENT}.env
EOF
    
    if [ $? -eq 0 ]; then
        log_success "Deployment to $server_ip completed successfully"
    else
        log_error "Deployment to $server_ip failed"
        exit 1
    fi
}

# Main deployment logic
if [ "$SERVER_IP" = "localhost" ]; then
    deploy_local
else
    deploy_remote "$SERVER_IP"
fi

log_success "Deployment completed successfully!"
log_info "Application should be available at: http://${SERVER_IP}:${APP_PORT}"
log_info "Health check: http://${SERVER_IP}:${APP_PORT}/health"
