📘 Project Overview
You are building a generic, reusable DevOps pipeline library in Groovy, versioned in GitLab, 
that will be consumed by Jenkins multibranch pipelines. The library supports deployment for 
Spring Boot and Angular applications, leveraging prebuilt containers by technology and version, 
and deploying to environment-specific servers based on Git branch or tag naming conventions.
🧱 Architectural Scope
Technologies
Area Tool/Tech
SCM GitLab
CI/CD Jenkins
Language Groovy (for Shared Library)
Backend Spring Boot (Java + Maven)
Frontend Angular (Node + npm)
Containers Docker
Registry Harbor
Static Analysis SonarQube
Deployment Docker Compose or Docker CLI
Artifact Storage Harbor (used as Artifactory)
🌿 Branching Strategy & Environments
Branch/Tag
Pattern Behavior Deployment
Target
develop Full build + deploy Development 
servers
release/
x.y.z Full build + deploy Test/UAT servers
tag/x.y.z No build; only deploy previously built artifact from 
corresponding release/x.y.z
Pre-Prod + 
Production
x.y.z follows semantic versioning (e.g., release/1.0.0, tag/1.0.0)
🧾 High-Level Requirements
1. Reusable Groovy Library
• Stored in GitLab
• Contains modular scripts under vars/ and src/org/...
• Dynamically chooses flow based on Git branch/tag
2. Prebuilt Containers
• Stored in Harbor (e.g., java17:latest, node18:latest)
• Selected based on application type and version
• Used for isolated build execution and tool consistency
3. Environment-specific Server Lists
• Defined as part of pipeline configuration per branch
• Example:
devServers = ['********', '********']
testServers = ['********']
preProdServers = ['********']
prodServers = ['********', '********']
🧾 Detailed Pipeline Behavior by Branch
For develop and release/*:
• Perform all pipeline steps: build, test, scan, deploy
For tag/*:
• Skip build, unit test, sonar scan
• Pull Docker image from Harbor (built in release/*)
• Deploy only
• Run post-deploy smoke test
🧰 Pipeline Parameters
Name Type Description
appType String springboot or angular
buildContainerImage String Harbor image to use for build (per tech/ver)
branchName String Current Git branch or tag
repoUrl String GitLab repository
sonarProjectKey String SonarQube key
dockerImageTag String Tag for Docker image
artifactRepoUrl String Harbor registry URL
envTarget String Environment: dev, test, preprod, prod
targetServers List IPs/hostnames for the target environment
emailRecipients List Notification targets
appPort Int Application port for health checks
🔁 Pipeline Execution Steps
1. Pull Build Container (if applicable)
• From Harbor: harbor.company.com/build-images/${appType}:$
{version}
2. Git Checkout
• Checkout from the provided repo and branch/tag
3. Code Analysis (Static)
• Log files like Dockerfile and Jenkinsfile in the root directory
4. Build Code (skip for tag/*)
• Spring Boot: mvn clean install
• Angular: npm install && npm run build
5. Unit Test (skip for tag/*)
• Spring Boot: mvn test
• Angular: npm test
6. Quality Gate (skip for tag/*)
• SonarQube scan and gate enforcement
7. Upload to Artifactory (skip for tag/*)
• Push .jar, .war, or Angular build to Jenkins
• Build and push Docker image to Harbor registry
8. Download from Artifactory
• Pull Docker image using docker pull ${artifactRepoUrl}/$
{dockerImageTag}
9. Container Node Checkout and Unstash
• SSH into all targetServers
• Prepare Docker environment (pull image, clean up old containers)
10. Run Compose
• Either via docker run or docker-compose with template override
11. Smoke Test
• HTTP call: curl -s --fail http://${server}:${appPort}/health
• Validate 200 OK or retry up to 3 times
12. Email Notification
• Summarize deployment status
• Include image tag, servers, endpoint link, and branch/tag
📁 Suggested GitLab Library Structure
jenkins-lib/
├── vars/
│ └── deployApp.groovy # Main pipeline entry point
├── src/org/devops/
│ ├── BuildUtils.groovy
│ ├── DeployUtils.groovy
│ ├── SonarUtils.groovy
│ ├── DockerUtils.groovy
│ └── NotificationUtils.groovy
└── resources/
 └── default-config.yml # Fallback values
✅ Example Branch Logic (Groovy)
if (branch.startsWith('tag/')) {
 skipBuild = true
 envTarget = 'preprod'
} else if (branch == 'develop') {
 envTarget = 'dev'
} else if (branch.startsWith('release/')) {
 envTarget = 'test'
}
✅ Success Criteria
• Multibranch Jenkins pipeline behaves differently for develop, release, and tag
• Shared Groovy library is reusable, generic, and stored in GitLab
• Artifacts and images are versioned and stored in Harbor
• Deployment is fully automated with post-deploy smoke test
• Email reporting works with full audit trail
Example:
1. deployApp.groovy – the main orchestrator script in your Jenkins shared library.
2. Supporting modular class DeployUtils.groovy.
3. A sample Jenkinsfile using the shared library.
4. A comment on how this works for multibranch scenarios like develop, 
release/x.y.z, and tag/x.y.z.
📁 vars/deployApp.groovy
def call(Map config = [:]) {
 def branch = env.BRANCH_NAME
 def skipBuild = false
 def deployEnv
 def servers
 // Determine environment and behavior based on branch
 if (branch.startsWith("tag/")) {
 skipBuild = true
 deployEnv = "preprod"
 servers = config.preProdServers + config.prodServers
 } else if (branch.startsWith("release/")) {
 deployEnv = "test"
 servers = config.testServers
 } else if (branch == "develop") {
 deployEnv = "dev"
 servers = config.devServers
 } else {
 error "Unsupported branch: $branch"
 }
 node {
 if (!skipBuild) {
 stage("Pull Build Container") {
 docker.image(config.buildContainerImage).inside {
 deployWorkflow(config, servers, skipBuild)
 }
 }
 } else {
 deployWorkflow(config, servers, skipBuild)
 }
 }
}
def deployWorkflow(config, servers, skipBuild) {
 def utils = new org.devops.DeployUtils(this)
 stage('Git Checkout') {
 checkout scm
 }
 stage('Code Analysis') {
 utils.analyzeCode()
 }
 if (!skipBuild) {
 utils.buildProject(config.appType)
 utils.runUnitTests(config.appType)
 utils.runSonarScan(config.sonarProjectKey, config.appType)
 utils.uploadToArtifactory(config.artifactRepoUrl, 
config.dockerImageTag)
 }
 utils.downloadContainerImage(config.artifactRepoUrl, 
config.dockerImageTag)
 utils.deployToVMs(servers, config.artifactRepoUrl, 
config.dockerImageTag, config.appPort)
 utils.smokeTest(servers, config.appPort)
 utils.sendEmail(
 config.emailRecipients,
 "Deployment Completed for ${env.BRANCH_NAME}",
 "Deployment for ${env.BRANCH_NAME} completed successfully 
on ${servers.join(', ')}"
 )
}
📁 src/org/devops/DeployUtils.groovy
package org.devops
class DeployUtils implements Serializable {
 def steps
 DeployUtils(steps) {
 this.steps = steps
 }
 def analyzeCode() {
 steps.sh 'find . -name "Dockerfile" -o -name 
"Jenkinsfile"'
 }
 def buildProject(String appType) {
 steps.stage("Build Project") {
 if (appType == 'springboot') {
 steps.sh 'mvn clean package -DskipTests'
 } else if (appType == 'angular') {
 steps.sh 'npm install && npm run build'
 }
 }
 }
 def runUnitTests(String appType) {
 steps.stage("Unit Tests") {
 if (appType == 'springboot') {
 steps.sh 'mvn test'
 } else if (appType == 'angular') {
 steps.sh 'npm test'
 }
 }
 }
 def runSonarScan(String sonarKey, String appType) {
 steps.stage("SonarQube Quality Gate") {
 steps.withSonarQubeEnv('SonarQube') {
 if (appType == 'springboot') {
 steps.sh "mvn sonar:sonar -Dsonar.projectKey=$
{sonarKey}"
 } else {
 steps.sh "sonar-scanner -Dsonar.projectKey=$
{sonarKey}"
 }
 }
 }
 }
 def uploadToArtifactory(String repo, String tag) {
 steps.stage("Upload Artifact") {
 steps.sh "docker build -t ${repo}/${tag} ."
 steps.sh "docker push ${repo}/${tag}"
 }
 }
 def downloadContainerImage(String repo, String tag) {
 steps.stage("Download Container Image") {
 steps.sh "docker pull ${repo}/${tag}"
 }
 }
 def deployToVMs(List servers, String repo, String tag, int 
appPort) {
 steps.stage("Deploy to Servers") {
 servers.each { vm ->
 steps.sh """
 ssh -o StrictHostKeyChecking=no user@${vm} '
 docker pull ${repo}/${tag}
 docker stop app || true
 docker rm app || true
 docker run -d --name app -p ${appPort}:$
{appPort} ${repo}/${tag}
 '
 """
 }
 }
 }
 def smokeTest(List servers, int port) {
 steps.stage("Smoke Test") {
 servers.each { vm ->
 steps.sh "curl -s --fail 
http://${vm}:${port}/health || exit 1"
 }
 }
 }
 def sendEmail(List recipients, String subject, String body) {
 steps.stage("Send Email") {
 steps.mail(
 to: recipients.join(','),
 subject: subject,
 body: body
 )
 }
 }
}
📄 Sample Jenkinsfile Using the Library
@Library('jenkins-lib') _
deployApp(
 appType: 'springboot',
 buildContainerImage: 
'harbor.company.com/build-images/java17:latest',
 sonarProjectKey: 'my-spring-app',
 artifactRepoUrl: 'harbor.company.com/myapps',
 dockerImageTag: 'my-spring-app:1.0.0',
 devServers: ['********01', '********02'],
 testServers: ['********0'],
 preProdServers: ['********'],
 prodServers: ['********', '********'],
 appPort: 8080,
 emailRecipients: ['<EMAIL>']
)
🧪 Branch Behavior Summary
Branch/Tag Build Unit Test Sonar Upload Deploy Target Servers
develop ✅ ✅ ✅ ✅ ✅ devServers
release/1.0.0 ✅ ✅ ✅ ✅ ✅ testServers
tag/1.0.0 ❌ ❌ ❌ ❌ ✅ preProdServers + prodServers