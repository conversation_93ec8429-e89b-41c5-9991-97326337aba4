version: '3.8'

services:
  # Spring Boot Application for Production
  smarthrms-app:
    image: harbor.company.com/myapps/smarthrms-dev:${IMAGE_TAG:-latest}
    container_name: smarthrms-prod
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: ${DB_URL:-**************************************}
      SPRING_DATASOURCE_USERNAME: ${DB_USERNAME:-smarthrms_user}
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD:-smarthrms_pass}
      SPRING_DATASOURCE_DRIVER_CLASS_NAME: com.mysql.cj.jdbc.Driver
      SPRING_JPA_HIBERNATE_DDL_AUTO: validate
      SPRING_JPA_SHOW_SQL: false
      SERVER_PORT: 8089
      JAVA_OPTS: "-Xms512m -Xmx1024m -XX:+UseG1GC"
    ports:
      - "8089:8089"
    networks:
      - smarthrms-prod-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8089/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  smarthrms-prod-network:
    external: true
