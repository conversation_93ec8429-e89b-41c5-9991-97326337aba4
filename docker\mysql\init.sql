-- Initialize smarthrms database
CREATE DATABASE IF NOT EXISTS smarthrms;
USE smarthrms;

-- Create a sample table for testing
CREATE TABLE IF NOT EXISTS health_check (
    id INT AUTO_INCREMENT PRIMARY KEY,
    status VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert initial data
INSERT INTO health_check (status) VALUES ('HEALTHY');

-- Grant permissions
GRANT ALL PRIVILEGES ON smarthrms.* TO 'smarthrms_user'@'%';
FLUSH PRIVILEGES;
