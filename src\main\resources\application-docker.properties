# Docker environment configuration
spring.application.name=smarthrms-dev
server.port=8089

# Database configuration for Docker
spring.datasource.url=*********************************
spring.datasource.username=smarthrms_user
spring.datasource.password=smarthrms_pass
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# Connection pool settings
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.idle-timeout=300000

# Logging
logging.level.com.techvg.dev=DEBUG
logging.level.org.springframework.web=INFO
