#!/bin/bash

# Server setup script for smarthrms-dev deployment
# This script prepares a server for application deployment
# Usage: ./setup-server.sh [server_ip]

set -e

# Configuration
SERVER_IP=${1:-"localhost"}
DEPLOYER_USER="deployer"
APP_NAME="smarthrms-dev"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to setup local server
setup_local() {
    log_info "Setting up local server..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if <PERSON><PERSON> is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker service."
        exit 1
    fi
    
    log_success "Docker is installed and running"
    
    # Create necessary directories
    log_info "Creating application directories..."
    mkdir -p /opt/${APP_NAME}/{logs,data,config}
    
    # Set permissions
    chmod 755 /opt/${APP_NAME}
    chmod 755 /opt/${APP_NAME}/{logs,data,config}
    
    log_success "Local server setup completed"
}

# Function to setup remote server
setup_remote() {
    local server_ip=$1
    log_info "Setting up remote server: $server_ip"
    
    # Test SSH connectivity
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "root@${server_ip}" exit 2>/dev/null; then
        log_error "Cannot connect to $server_ip via SSH. Please check connectivity and SSH keys."
        exit 1
    fi
    
    log_success "SSH connectivity to $server_ip verified"
    
    # Execute setup commands on remote server
    ssh "root@${server_ip}" << 'EOF'
        set -e
        
        echo "=== Starting server setup ==="
        
        # Update system packages
        echo "=== Updating system packages ==="
        apt-get update -y
        
        # Install required packages
        echo "=== Installing required packages ==="
        apt-get install -y curl wget git unzip
        
        # Install Docker if not present
        if ! command -v docker &> /dev/null; then
            echo "=== Installing Docker ==="
            curl -fsSL https://get.docker.com -o get-docker.sh
            sh get-docker.sh
            rm get-docker.sh
            
            # Start and enable Docker service
            systemctl start docker
            systemctl enable docker
        else
            echo "=== Docker already installed ==="
        fi
        
        # Create deployer user if not exists
        if ! id "deployer" &>/dev/null; then
            echo "=== Creating deployer user ==="
            useradd -m -s /bin/bash deployer
            usermod -aG docker deployer
        else
            echo "=== Deployer user already exists ==="
        fi
        
        # Create application directories
        echo "=== Creating application directories ==="
        mkdir -p /opt/smarthrms-dev/{logs,data,config}
        chown -R deployer:deployer /opt/smarthrms-dev
        chmod 755 /opt/smarthrms-dev
        chmod 755 /opt/smarthrms-dev/{logs,data,config}
        
        # Create SSH directory for deployer
        mkdir -p /home/<USER>/.ssh
        chown deployer:deployer /home/<USER>/.ssh
        chmod 700 /home/<USER>/.ssh
        
        # Configure firewall (if ufw is available)
        if command -v ufw &> /dev/null; then
            echo "=== Configuring firewall ==="
            ufw allow 22/tcp    # SSH
            ufw allow 8089/tcp  # Application port
            ufw --force enable
        fi
        
        # Create log rotation configuration
        echo "=== Setting up log rotation ==="
        cat > /etc/logrotate.d/smarthrms-dev << 'LOGROTATE_EOF'
/opt/smarthrms-dev/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 deployer deployer
}
LOGROTATE_EOF
        
        echo "=== Server setup completed successfully ==="
EOF
    
    if [ $? -eq 0 ]; then
        log_success "Remote server setup completed successfully"
    else
        log_error "Remote server setup failed"
        exit 1
    fi
}

# Function to setup SSH keys for deployer
setup_ssh_keys() {
    local server_ip=$1
    
    log_info "Setting up SSH keys for deployer user..."
    
    # Check if SSH key exists locally
    if [ ! -f ~/.ssh/id_rsa.pub ]; then
        log_warning "SSH public key not found. Generating new SSH key pair..."
        ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
    fi
    
    # Copy SSH key to remote server
    if [ "$server_ip" != "localhost" ]; then
        log_info "Copying SSH key to $server_ip..."
        ssh-copy-id -i ~/.ssh/id_rsa.pub "deployer@${server_ip}"
        
        # Test SSH connectivity with deployer user
        if ssh -o ConnectTimeout=10 -o BatchMode=yes "deployer@${server_ip}" exit 2>/dev/null; then
            log_success "SSH key setup completed for deployer user"
        else
            log_error "SSH key setup failed for deployer user"
            exit 1
        fi
    fi
}

# Function to verify Harbor registry connectivity
verify_harbor_connectivity() {
    local server_ip=$1
    
    log_info "Verifying Harbor registry connectivity..."
    
    local test_command="docker pull hello-world && docker rmi hello-world"
    
    if [ "$server_ip" = "localhost" ]; then
        eval "$test_command" &>/dev/null
    else
        ssh "deployer@${server_ip}" "$test_command" &>/dev/null
    fi
    
    if [ $? -eq 0 ]; then
        log_success "Docker registry connectivity verified"
    else
        log_warning "Docker registry connectivity test failed (this may be normal if Harbor requires authentication)"
    fi
}

# Main execution
log_info "=== Starting server setup for $SERVER_IP ==="

if [ "$SERVER_IP" = "localhost" ]; then
    setup_local
else
    setup_remote "$SERVER_IP"
    setup_ssh_keys "$SERVER_IP"
fi

verify_harbor_connectivity "$SERVER_IP"

log_success "=== Server setup completed successfully! ==="
log_info "Server $SERVER_IP is now ready for application deployment"

# Display next steps
log_info "=== Next Steps ==="
log_info "1. Configure Jenkins credentials for SSH access"
log_info "2. Configure Harbor registry credentials"
log_info "3. Test deployment with: ./deploy.sh dev smarthrms-dev:latest $SERVER_IP"
log_info "4. Run health check with: ./health-check.sh $SERVER_IP"
