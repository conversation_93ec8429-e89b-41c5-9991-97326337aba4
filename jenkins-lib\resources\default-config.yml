# Default configuration for <PERSON> shared library
# These values can be overridden in individual Jenkinsfiles

# Application defaults
appType: "springboot"
appName: "smarthrms-dev"
appPort: 8089

# Build configuration
buildContainerImage: "harbor.company.com/build-images/java21:latest"
javaVersion: "21"
mavenVersion: "3.9.4"

# Registry configuration
artifactRepoUrl: "harbor.company.com/myapps"
harborRegistry: "harbor.company.com"
harborProject: "myapps"

# SonarQube configuration
sonarProjectKey: "smarthrms-dev"
sonarHost: "http://sonarqube.company.com:9000"

# Environment servers (as per devops.txt specification)
devServers:
  - "**********"
  - "**********"

testServers:
  - "*********"

preProdServers:
  - "********"

prodServers:
  - "********"
  - "********"

# Notification settings
emailRecipients:
  - "<EMAIL>"
  - "<EMAIL>"

# Deployment settings
deploymentTimeout: 300  # 5 minutes
healthCheckRetries: 3
healthCheckInterval: 10  # seconds

# Docker settings
dockerBuildArgs:
  - "JAVA_VERSION=21"
  - "APP_NAME=smarthrms-dev"

# SSH settings
sshUser: "deployer"
sshKeyCredentialsId: "ssh-deployer-key"

# Harbor credentials
harborCredentialsId: "harbor-credentials"
